function _instanceof(left, right) {
    if (null != right && "undefined" != typeof Symbol && right[Symbol.hasInstance]) return !!right[Symbol.hasInstance](left);
    return left instanceof right;
}
function _type_of(obj) {
    return obj && "undefined" != typeof Symbol && obj.constructor === Symbol ? "symbol" : typeof obj;
}
var isElement = function(el) {
    var isElem;
    isElem = ("undefined" == typeof HTMLElement ? "undefined" : _type_of(HTMLElement)) === 'object' ? _instanceof(el, HTMLElement) : !!el && (void 0 === el ? "undefined" : _type_of(el)) === 'object' && 1 === el.nodeType && 'string' == typeof el.nodeName;
    return isElem;
};
export { isElement };
