import { getID } from "./getID.js";
import { getHref } from "./getHref.js";
import { getClassSelectors } from "./getClasses.js";
import { getCombinations } from "./getCombinations.js";
import { getAttributes } from "./getAttributes.js";
import { getNthChild } from "./getNthChild.js";
import { getTag } from "./getTag.js";
import { getName } from "./getName.js";
import { isUnique } from "./isUnique.js";
import { getParents } from "./getParents.js";
function _instanceof(left, right) {
    if (null != right && "undefined" != typeof Symbol && right[Symbol.hasInstance]) return !!right[Symbol.hasInstance](left);
    return left instanceof right;
}
var getAllSelectors = function(el, selectors, attributesToIgnore) {
    var funcs = {
        Tag: getTag,
        NthChild: getNthChild,
        Attributes: function(elem) {
            return getAttributes(elem, attributesToIgnore);
        },
        Class: getClassSelectors,
        ID: getID,
        Name: getName,
        Href: getHref
    };
    return selectors.reduce(function(res, next) {
        res[next] = funcs[next](el);
        return res;
    }, {});
};
var testUniqueness = function(element, selector) {
    var parentNode = element.parentNode;
    if (!parentNode) return false;
    var elements = parentNode.querySelectorAll(selector);
    return 1 === elements.length && elements[0] === element;
};
var getFirstUnique = function(element, selectors) {
    return selectors.find(testUniqueness.bind(null, element));
};
var getUniqueCombination = function(element, items, tag) {
    var combinations = getCombinations(items, 3);
    var firstUnique = getFirstUnique(element, combinations);
    if (firstUnique) return firstUnique;
    if (tag) {
        combinations = combinations.map(function(combination) {
            return tag + combination;
        });
        firstUnique = getFirstUnique(element, combinations);
        if (firstUnique) return firstUnique;
    }
    return null;
};
var getUniqueSelector = function(element, selectorTypes, attributesToIgnore, excludeRegex) {
    var foundSelector;
    var elementSelectors = getAllSelectors(element, selectorTypes, attributesToIgnore);
    if (excludeRegex && _instanceof(excludeRegex, RegExp)) {
        var idSelector = elementSelectors.ID;
        if ('string' == typeof idSelector) elementSelectors.ID = excludeRegex.test(idSelector) ? null : idSelector;
        var classSelectors = elementSelectors.Class;
        if (Array.isArray(classSelectors)) elementSelectors.Class = classSelectors.filter(function(className) {
            return !excludeRegex.test(className);
        });
    }
    var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = void 0;
    try {
        for(var _iterator = selectorTypes[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){
            var selectorType = _step.value;
            var ID = elementSelectors.ID, Tag = elementSelectors.Tag, Name = elementSelectors.Name, Href = elementSelectors.Href, Classes = elementSelectors.Class, Attributes = elementSelectors.Attributes, NthChild = elementSelectors.NthChild;
            switch(selectorType){
                case 'ID':
                    if ('string' == typeof ID && testUniqueness(element, ID)) return ID;
                    break;
                case 'Tag':
                    if ('string' == typeof Tag && testUniqueness(element, Tag)) return Tag;
                    break;
                case 'Name':
                    if ('string' == typeof Name && testUniqueness(element, Name)) return Name;
                    break;
                case 'Href':
                    if ('string' == typeof Href && testUniqueness(element, Href)) return Href;
                    break;
                case 'Class':
                    if (Array.isArray(Classes) && Classes.length > 0) {
                        foundSelector = getUniqueCombination(element, Classes, 'string' == typeof Tag ? Tag : void 0);
                        if (foundSelector) return foundSelector;
                    }
                    break;
                case 'Attributes':
                    if (Array.isArray(Attributes) && Attributes.length > 0) {
                        foundSelector = getUniqueCombination(element, Attributes, 'string' == typeof Tag ? Tag : void 0);
                        if (foundSelector) return foundSelector;
                    }
                    break;
                case 'NthChild':
                    if ('string' == typeof NthChild) return NthChild;
                    break;
            }
        }
    } catch (err) {
        _didIteratorError = true;
        _iteratorError = err;
    } finally{
        try {
            if (!_iteratorNormalCompletion && null != _iterator["return"]) _iterator["return"]();
        } finally{
            if (_didIteratorError) throw _iteratorError;
        }
    }
    return '*';
};
function unique(el) {
    var options = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    var _options_selectorTypes = options.selectorTypes, selectorTypes = void 0 === _options_selectorTypes ? [
        'ID',
        'Class',
        'Tag',
        'Name',
        'NthChild'
    ] : _options_selectorTypes, _options_attributesToIgnore = options.attributesToIgnore, attributesToIgnore = void 0 === _options_attributesToIgnore ? [
        'id',
        'class',
        'length'
    ] : _options_attributesToIgnore, _options_excludeRegex = options.excludeRegex, excludeRegex = void 0 === _options_excludeRegex ? null : _options_excludeRegex;
    var allSelectors = [];
    var parents = getParents(el);
    var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = void 0;
    try {
        for(var _iterator = parents[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){
            var elem = _step.value;
            var selector = getUniqueSelector(elem, selectorTypes, attributesToIgnore, excludeRegex);
            if (selector) allSelectors.push(selector);
        }
    } catch (err) {
        _didIteratorError = true;
        _iteratorError = err;
    } finally{
        try {
            if (!_iteratorNormalCompletion && null != _iterator["return"]) _iterator["return"]();
        } finally{
            if (_didIteratorError) throw _iteratorError;
        }
    }
    var selectors = [];
    var _iteratorNormalCompletion1 = true, _didIteratorError1 = false, _iteratorError1 = void 0;
    try {
        for(var _iterator1 = allSelectors[Symbol.iterator](), _step1; !(_iteratorNormalCompletion1 = (_step1 = _iterator1.next()).done); _iteratorNormalCompletion1 = true){
            var it = _step1.value;
            selectors.unshift(it);
            var selector1 = selectors.join(' > ');
            if (isUnique(el, selector1)) return selector1;
        }
    } catch (err) {
        _didIteratorError1 = true;
        _iteratorError1 = err;
    } finally{
        try {
            if (!_iteratorNormalCompletion1 && null != _iterator1["return"]) _iterator1["return"]();
        } finally{
            if (_didIteratorError1) throw _iteratorError1;
        }
    }
    return null;
}
export { unique as default };
