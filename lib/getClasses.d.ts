/**
 * Get class names for an element
 *
 * @param { Element } el - DOM element
 * @return { string[] } Array of class names
 */
declare const getClasses: (el: Element) => string[];
/**
 * Returns the Class selectors of the element
 * @param  { Element } el - DOM element
 * @return { string[] } Array of class selectors
 */
declare const getClassSelectors: (el: Element) => string[];
export { getClasses, getClassSelectors };
