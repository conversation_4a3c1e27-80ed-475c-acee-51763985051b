var getAttributes = function(el) {
    var attributesToIgnore = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [
        'id',
        'class',
        'length'
    ];
    var attributes = el.attributes;
    var attrs = Array.from(attributes);
    return attrs.reduce(function(sum, next) {
        if (!(attributesToIgnore.indexOf(next.nodeName) > -1)) sum.push("[".concat(next.nodeName, '="').concat(next.value, '"]'));
        return sum;
    }, []);
};
export { getAttributes };
