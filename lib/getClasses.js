var getClasses = function(el) {
    if (!el.hasAttribute('class')) return [];
    try {
        var classList = Array.prototype.slice.call(el.classList);
        return classList.filter(function(item) {
            return /^[a-z_-][a-z\d_-]*$/i.test(item) ? item : null;
        }).filter(Boolean);
    } catch (e) {
        var className = el.getAttribute('class');
        if (!className) return [];
        className = className.trim().replace(/\s+/g, ' ');
        return className.split(' ');
    }
};
var getClassSelectors = function(el) {
    var classList = getClasses(el).filter(Boolean);
    return classList.map(function(cl) {
        return ".".concat(CSS.escape(cl));
    });
};
export { getClassSelectors, getClasses };
