var kCombinations = function(result, items, data, start, end, index, k) {
    if (index === k) return void result.push(data.slice(0, index).join(''));
    for(var i = start; i <= end && end - i + 1 >= k - index; ++i){
        data[index] = items[i];
        kCombinations(result, items, data, i + 1, end, index + 1, k);
    }
};
var getCombinations = function(items, k) {
    var result = [];
    var n = items.length;
    var data = [];
    for(var l = 1; l <= k; ++l)kCombinations(result, items, data, 0, n - 1, 0, l);
    return result;
};
export { getCombinations };
