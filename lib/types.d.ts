/**
 * Core type definitions for unique-selector library
 */
/**
 * Supported selector types for generating unique selectors
 */
export type SelectorType = 'ID' | 'Class' | 'Tag' | 'NthChild' | 'Attributes';
/**
 * Options interface for the unique selector function
 */
export interface UniqueOptions {
    /**
     * Array of selector types based on which the unique selector will be generated
     * @default ['ID', 'Class', 'Tag', 'NthChild']
     */
    selectorTypes?: SelectorType[];
    /**
     * Array of attribute names to ignore when generating attribute selectors
     * @default ['id', 'class', 'length']
     */
    attributesToIgnore?: string[];
    /**
     * Regular expression to exclude certain ID and class names
     * @default null
     */
    excludeRegex?: RegExp | null;
}
/**
 * Object containing all possible selectors for an element
 */
export interface ElementSelectors {
    [key: string]: string | string[] | null | undefined;
    ID?: string | null;
    Tag?: string;
    Class?: string[];
    Attributes?: string[];
    NthChild?: string | null;
}
/**
 * Function type for selector generation functions
 */
export type SelectorFunction = (element: Element) => string | string[] | null;
/**
 * Map of selector types to their corresponding functions
 */
export interface SelectorFunctionMap {
    Tag: (el: Element) => string;
    NthChild: (element: Element) => string | null;
    Attributes: (elem: Element, attributesToIgnore?: string[]) => string[];
    Class: (el: Element) => string[];
    ID: (el: Element) => string | null;
}
/**
 * Type for combination generation result
 */
export type CombinationResult = string[];
/**
 * Type for element validation function
 */
export type ElementValidator = (el: unknown) => el is Element;
/**
 * Type for uniqueness test function
 */
export type UniquenessTest = (element: Element, selector: string) => boolean;
