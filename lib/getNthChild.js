import { isElement } from "./isElement.js";
var getNthChild = function(element) {
    var counter = 0;
    var k;
    var sibling;
    var parentNode = element.parentNode;
    if (parentNode) {
        var childNodes = parentNode.childNodes;
        var len = childNodes.length;
        for(k = 0; k < len; k++){
            sibling = childNodes[k];
            if (isElement(sibling)) {
                counter++;
                if (sibling === element) return ":nth-child(".concat(counter, ")");
            }
        }
    }
    return null;
};
export { getNthChild };
