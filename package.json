{"name": "@flow-ez/unique-selector", "version": "0.0.1", "description": "Given a DOM node, return a unique CSS selector matching only that element", "keywords": ["dom", "html", "css", "selector", "event"], "license": "MIT", "author": "Flow EZ Team", "type": "module", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.js"}}, "types": "./lib/index.d.ts", "files": ["lib"], "scripts": {"build": "rslib build", "dev": "rslib build --watch", "format": "prettier --write .", "lint": "eslint ."}, "devDependencies": {"@eslint/js": "^9.30.0", "@rslib/core": "^0.10.6", "@types/node": "^22.16.4", "eslint": "^9.30.0", "globals": "^16.2.0", "prettier": "^3.6.2", "prettier-plugin-packagejson": "^2.5.18", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1"}, "git": {"type": "git", "url": "https://github.com/Flow-EZ/unique-selector.git"}}